# 时间筛选器接口调用顺序修复

## 问题分析

### 原始问题

时间筛选器在默认值变更时，接口调用顺序不正确：

1. 先调用查询接口 (`queryElementResultData`)
2. 再调用保存接口 (`saveElementStructureInfos`)

### 期望的正确顺序

应该像列表筛选器一样：

1. 先调用保存接口 (`saveElementStructureInfos`)
2. 保存成功后再调用查询接口 (`queryElementResultData`)

### 问题根因

在默认值设置器中：

1. `onChange(value)` 先被调用，更新 `defaultValue` prop
2. 这触发了时间筛选器组件的 `useEffect([..., defaultValue])`，立即调用查询接口
3. 然后 `setPropValue('dataSetConfig')` 才触发保存接口

## 解决方案

### 采用延迟查询的简单方案

由于复杂的 `meta.query` 方案会导致死循环，我们采用了一个更简单的解决方案：

**核心思路**：在默认值变更时，延迟触发查询，给保存接口时间先执行。

### 关键修改点

1. **检测默认值变更**

   - 通过比较 `defaultValueRef.current` 和 `defaultValue` 来判断是否是真正的默认值变更
   - 区分初始化和配置变更两种情况

2. **延迟查询触发**
   - 初始化时：立即触发查询（延迟 0ms）
   - 默认值变更时：延迟 500ms 触发查询，给保存接口足够时间先执行

### 代码变更

#### 关键代码片段

```typescript
// 延迟触发查询，给保存接口时间先执行
// 如果是默认值变更（不是初始化），延迟更长时间
const isDefaultValueChange =
  JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue);
const delay = isDefaultValueChange ? 500 : 0; // 默认值变更时延迟500ms

setTimeout(() => {
  console.log('触发查询，延迟时间:', delay);
  relationCenter.readyFilter(`date_${filterId}`);
}, delay);
```

## 验证方法

1. 在时间筛选器中修改默认值配置
2. 观察网络请求顺序：
   - 应该先看到 `saveElementStructureInfos` 请求
   - 500ms 后看到 `queryElementResultData` 请求
3. 观察控制台日志：
   - 初始化时应该显示 "触发查询，延迟时间: 0"
   - 默认值变更时应该显示 "触发查询，延迟时间: 500"

## 优势

1. **简单可靠**：不会导致死循环或复杂的依赖问题
2. **最小修改**：只修改了查询触发的时机，没有改变核心逻辑
3. **向后兼容**：不影响现有的初始化流程
