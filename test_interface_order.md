# 时间筛选器接口调用顺序修复

## 问题分析

### 原始问题
时间筛选器在默认值变更时，接口调用顺序不正确：
1. 先调用查询接口 (`queryElementResultData`)
2. 再调用保存接口 (`saveElementStructureInfos`)

### 期望的正确顺序
应该像列表筛选器一样：
1. 先调用保存接口 (`saveElementStructureInfos`)
2. 保存成功后再调用查询接口 (`queryElementResultData`)

## 解决方案

### 关键修改点

1. **添加 meta.query 方法**
   - 时间筛选器现在设置了 `meta.query` 方法
   - 这个方法会在配置保存成功后被调用（通过 `callSaveComponentsData` 的回调）

2. **修改初始化逻辑**
   - 在默认值变更时，不立即调用 `relationCenter.readyFilter()`
   - 只在初始化时调用 `readyFilter()`，配置变更时通过 `meta.query` 触发

3. **接口调用流程**
   ```
   默认值变更 → setPropValue('dataSetConfig') → onChangeNodeProp 事件 → 
   callSavePageStructure() → savePageStructure 接口 → 
   callSaveComponentsData() → saveElementStructureInfos 接口 → 
   meta.query() → relationCenter.readyFilter() → queryElementResultData 接口
   ```

### 代码变更

#### 主要修改
1. 添加了 `setQueryParams` 函数来封装查询参数设置逻辑
2. 添加了 `meta.query` 方法，在配置保存成功后被调用
3. 修改了初始化逻辑，避免在配置变更时立即触发查询

#### 关键代码片段
```typescript
// 设置 meta.query 方法，这个方法会在配置保存成功后被调用
useEffect(() => {
  setMeta(
    {
      query() {
        // 当配置变更时触发，重新设置查询参数并触发查询
        setQueryParams();
        // 触发筛选器就绪，开始查询数据
        setTimeout(() => {
          relationCenter.readyFilter(`date_${filterId}`);
        }, 0);
      },
    },
    true,
  );
}, [setQueryParams, filterId]);
```

## 验证方法

1. 在时间筛选器中修改默认值配置
2. 观察网络请求顺序：
   - 应该先看到 `saveElementStructureInfos` 请求
   - 然后看到 `queryElementResultData` 请求

## 参考实现

参考了列表筛选器 (`ListFilter`) 的实现模式：
- `ListFilterDefaultValueSetter.tsx` 中的默认值变更逻辑
- `ListFilter/index.tsx` 中的 `meta.query` 设置方式
