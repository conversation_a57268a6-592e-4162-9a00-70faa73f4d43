/**
 * 日期组件
 */
import { useEffect, useState, useRef } from 'react';
import dayjs from 'dayjs';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import queryCenterExp from '../module/Query';
import { getOtherPresets } from '../../components/Filter/DatePickerFilter/tools';
import linkageCenterExp from '../module/Linkage';
import useComponent from '../../hooks/useComponent';
import { DEFAULT_DATE_RANGE } from './constants/dateRangeOptions';

interface DatePickerFilterProps {
  title?: string;
  dataSetConfig: any;
  __id: any;
  componentId: any;
  defaultValue: any;
  dateRange?: number;
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateOnly: true,
      indexDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {
      // 拖拽进入的，还需要配置数据集
      type: 'drop-component',
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DatePickerFilter = ({
  dataSetConfig,
  __id,
  componentId,
  defaultValue,
  dateRange = DEFAULT_DATE_RANGE,
  uuid,
}: DatePickerFilterProps) => {
  const linkageCenter = linkageCenterExp(uuid);
  const queryCenter = queryCenterExp(uuid);
  const relationCenter = relationCenterExp(uuid);
  const defaultValueRef = useRef(defaultValue);
  const filterId = __id ?? componentId ?? '';
  const { dimensionInfo } = dataSetConfig ?? {};
  const dimension = dimensionInfo?.[0];
  const [meta, setMeta] = useComponent(filterId);
  const presets = getOtherPresets();
  // 根据默认值类型获取对应的日期项
  const getDefaultDateItem = () => {
    const dateType = defaultValue?.dateType;
    // 外部自定义默认时间
    if (dateType === 'customDate') {
      // 如果 defaultValue 中有自定义的 value，使用它；否则使用默认值
      const customValue =
        defaultValue?.value || presets.find((item) => item.type === 'last-7');
      return {
        label: null,
        title: '自定义',
        timeType: 6,
        type: 'customDate',
        value: customValue,
      };
    }
    // 清空场景
    if (dateType === 'all') {
      return presets.find((item) => item.type === 'clear-time');
    }

    // 快捷默认时间
    return (
      presets.find((item) => item.type === dateType) ||
      presets.find((item) => item.type === 'last-7')
    );
  };

  const defaultDateItem = getDefaultDateItem();
  console.log(defaultValue, 'defaultValue99999', defaultDateItem);

  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());

  useEffect(() => {
    if (defaultValue?.dateType === 'customDate') {
      defaultValueRef.current = defaultValue;
      temp_dateMap[filterId] = {
        value:
          defaultValue?.value || presets.find((item) => item.type === 'last-7'),
        type: 6, // CUSTOM 类型
      };
      setKey(Date.now());
    } else if (
      JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
    ) {
      defaultValueRef.current = defaultValue;
      temp_dateMap[filterId] = null;
      setKey(Date.now());
    }
  }, [defaultValue]);

  // 监听时间跨度变化，手动重置为当前默认值
  useEffect(() => {
    const currentDefaultDateItem = getDefaultDateItem();
    temp_dateMap[filterId] = {
      value: currentDefaultDateItem?.value,
      type: temp_dateMap[filterId]?.type,
    };
    setKey(Date.now());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateRange]);

  // 编辑态，默认值发生变化则重新设置默认值，并且触发修改参数
  // 初始化
  useEffect(() => {
    if (dimension) {
      const defaultValueNew =
        temp_dateMap[filterId]?.value || defaultDateItem.value;
      relationCenter.registerFilter(`date_${filterId}`);
      queryCenter.setQuery(filterId, {
        columnId: dimension?.columnId,
        key: dimension?.key,
        dataType: dimension?.dataType,
        dateFilterRelativeUnit:
          temp_dateMap[filterId]?.type || defaultDateItem.timeType, // 默认是自定义粒度
        fieldValue: defaultValueNew?.length
          ? [
              dayjs(defaultValueNew[0]).startOf('day').valueOf(),
              dayjs(defaultValueNew[1]).endOf('day').valueOf(),
            ]
          : [],
        fieldLabel: defaultValueNew?.length
          ? [
              dayjs(defaultValueNew[0]).startOf('day').format('YYYY-MM-DD') +
                ' ~ ' +
                dayjs(defaultValueNew[1]).endOf('day').format('YYYY-MM-DD'),
            ]
          : [],
      });

      // 延迟触发查询，给保存接口时间先执行
      // 如果是默认值变更（不是初始化），延迟更长时间
      const isDefaultValueChange =
        JSON.stringify(defaultValueRef.current) !==
        JSON.stringify(defaultValue);
      const delay = isDefaultValueChange ? 500 : 0; // 默认值变更时延迟500ms

      setTimeout(() => {
        console.log('触发查询，延迟时间:', delay);
        relationCenter.readyFilter(`date_${filterId}`);
      }, delay);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dimension, filterId, defaultValue]);

  // 清理查询参数
  useEffect(() => {
    return () => {
      queryCenter.deleteQuery(filterId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterId]);
  // 重置筛选器：监听重置事件并恢复默认值
  useEffect(() => {
    const resetFilter = () => {
      // 强制刷新组件
      setKey(Date.now());
      // 清空临时存储
      temp_dateMap[filterId] = null;
      // 重置查询参数为默认值
      queryCenter.setQuery(filterId, {
        columnId: dimension?.columnId,
        key: dimension?.key,
        dataType: dimension?.dataType,
        dateFilterRelativeUnit: defaultDateItem?.timeType, // 默认是自定义粒度
        fieldValue: defaultDateItem?.value?.length
          ? [
              dayjs(defaultDateItem.value[0]).startOf('day').valueOf(),
              dayjs(defaultDateItem.value[1]).endOf('day').valueOf(),
            ]
          : [],
        fieldLabel: defaultDateItem?.value?.length
          ? [
              dayjs(defaultDateItem.value[0])
                .startOf('day')
                .format('YYYY-MM-DD') +
                ' ~ ' +
                dayjs(defaultDateItem.value[1])
                  .endOf('day')
                  .format('YYYY-MM-DD'),
            ]
          : [],
      });
    };
    linkageCenter.subscribe('reset', resetFilter);
    return () => {
      linkageCenter.unsubscribe('reset', resetFilter);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultDateItem]);
  const onChange = (fieldValue: any, dateFilterRelativeUnit: number) => {
    // 临时储存
    temp_dateMap[filterId] = {
      type: dateFilterRelativeUnit,
      value: fieldValue?.length ? [fieldValue[0], fieldValue[1]] : [],
    };
    queryCenter.setQuery(filterId, {
      columnId: dimension?.columnId,
      key: dimension?.key,
      dataType: dimension?.dataType,
      fieldValue,
      fieldLabel: fieldValue?.length
        ? [
            dayjs(fieldValue[0]).startOf('day').format('YYYY-MM-DD') +
              ' ~ ' +
              dayjs(fieldValue[1]).endOf('day').format('YYYY-MM-DD'),
          ]
        : [],
    });
  };
  //支持动态渲染
  if (dimension) {
    return (
      <div style={{ width: '100%' }}>
        <DatePickerFilterView
          key={key}
          pickerType="picker-filter"
          handleChange={onChange}
          defaultValue={defaultDateItem}
          maxStep={dateRange - 1}
        />
      </div>
    );
  } else {
    return <div className="lego-filter-wrap">请选择日期</div>;
  }
};

DatePickerFilter.displayName = '时间筛选器';
