{"version": "0.1.0", "private": true, "name": "leopard-web-qbi", "author": "<<EMAIL>>", "description": "乐高", "main": "lib/index.js", "files": ["build/", "lib/", "lowcode/", "lowcode_lib/", "lowcode_es/"], "scripts": {"dev": "BUILD_ENV=local max dev", "build": "max build", "build:report": "ANALYZE=1 max build", "format": "prettier --cache --write .", "prepare": "husky install", "postinstall": "max setup", "setup": "max setup", "start": "pnpm dev", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "lint": "", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "scan": "node node_modules/router-scan-bigbang --setting[plugins]:{Array}=InterfaceScanPlugin --setting[babelPlugins]:{Array}=transform-react-useModel --buildEnv=daily", "build:daily": "BUILD_ENV=daily UMI_ENV=test max build && npm run lowcode:build", "build:dev": "BUILD_ENV=dev UMI_ENV=test max build && npm run lowcode:build", "build:pre": "BUILD_ENV=pre UMI_ENV=production max build && npm run lowcode:build", "build:pre2": "BUILD_ENV=pre2 UMI_ENV=production max build && npm run lowcode:build", "build:prod": "BUILD_ENV=prod UMI_ENV=production max build && npm run lowcode:build", "deploy:cdn-v": "cross-env UMI_ENV=test max build && npm run lowcode:build && sh /data/scripts/deploy2oss.sh", "deploy:cdn-v-public": "cross-env UMI_ENV=production max build && npm run lowcode:build && sh /data/scripts/deploy2oss.sh production", "lowcode:dev": "build-scripts start --config ./build.lowcode.js", "lowcode:build": "sh ./copy.sh && rm -rf lowcode && rm -rf .tmp && rm -rf build && build-scripts build --config ./build.lowcode.js && npm run copy", "copy": "node config/copy.js", "bui": "build-scripts build --config ./build.lowcode.js", "lint-staged": "lint-staged", "build:lib": "build-scripts build --config ./build.lib.json"}, "dependencies": {"@alifd/next": "^1.26.35", "@alilc/lowcode-datasource-fetch-handler": "^1.1.4", "@alilc/lowcode-designer": "^1.2.3", "@alilc/lowcode-editor-core": "^1.2.3", "@alilc/lowcode-editor-skeleton": "^1.2.3", "@alilc/lowcode-engine": "^1.2.2", "@alilc/lowcode-engine-ext": "^1.0.6", "@alilc/lowcode-plugin-code-generator": "^1.0.6", "@alilc/lowcode-plugin-designer": "^1.2.3", "@alilc/lowcode-plugin-inject": "^1.2.3", "@alilc/lowcode-plugin-set-ref-prop": "^1.0.1", "@alilc/lowcode-react-renderer": "^1.2.5", "@alilc/lowcode-setter-behavior": "^1.0.0", "@alilc/lowcode-setter-title": "^1.0.2", "@alilc/lowcode-types": "^1.2.2", "@alilc/lowcode-utils": "^1.2.3", "@ant-design/icons": "^5.2.6", "@antv/g2plot": "^2.4.31", "@antv/util": "^3.3.4", "@babel/parser": "^7.23.6", "@babel/runtime": "^7.0.0", "@blm-fe/bos-proxy": "0.1.1-beta.416", "@blm/bi-lego-sdk": "0.3.2-beta.12", "@blmcp/charts": "0.3.3", "@blmcp/peento-businessComponents": "0.0.63", "@blmcp/peento-publicApiHub": "1.0.12", "@blmcp/peento-request": "0.0.19", "@blmcp/ui": "1.1.19", "@blmcp/ui-mobile": "1.1.0", "@blmcp/web-ui": "1.1.3-beta.3", "@blmlc/lego-init": "^1.0.8-beta.30", "@codemirror/autocomplete": "^6.16.0", "@codemirror/commands": "^6.6.0", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.26.3", "@dj-blm/lego-components": "1.0.2-beta.3", "@formily/antd-v5": "^1.1.1", "@formily/core": "^2.2.29", "@formily/react": "^2.2.29", "@formily/reactive": "^2.2.29", "@formily/reactive-react": "^2.2.29", "@ice/stark-app": "^1.5.0", "@peento/leopard-web-shareurl": "0.8.2", "@svgr/webpack": "^5.5.0", "@types/react": "^18.0.25", "@umijs/max": "4.0.78", "@umijs/plugin-model": "2.6.2", "ahooks": "3.7.8", "axios": "^1.6.2", "blm-utils": "1.2.88-rc.4", "classnames": "^2.5.0", "click-to-react-component": "1.1.0", "codemirror": "^6.0.1", "date-material-demo": "^0.7.0", "dayjs": "1.11.10", "echarts": "^5.4.3", "immer": "^10.0.3", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "lodash-es": "4.17.21", "md5": "^2.3.0", "mobx": "6.13.0", "node-uuid": "^1.4.8", "numeral": "^2.0.6", "rc-util": "^5.43.0", "rc-virtual-list": "^3.14.5", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-draggable": "4.4.6", "react-joyride": "^2.7.2", "react-resizable": "^3.0.5", "resize-observer-polyfill": "^1.5.1", "router-scan-bigbang": "^1.5.5", "stylelint": "^15.10.3", "use-immer": "^0.9.0"}, "devDependencies": {"@alib/build-scripts": "^0.1.32", "@alifd/build-plugin-lowcode": "^0.4.7", "@alilc/build-plugin-alt": "^1.3.4", "@blmcp/peento-umi-extension": "2.0.44", "@swc/core": "1.3.78", "@types/lodash-es": "4.17.8", "@types/numeral": "^2.0.5", "@types/react-dom": "^18.0.6", "@types/react-resizable": "^3.0.7", "build-plugin-component": "^1.12.2", "cross-env": "7.0.3", "eslint-plugin-import": "^2.29.1", "husky": "8.0.3", "lint-staged": "13.2.3", "prettier": "3.0.2", "prettier-plugin-organize-imports": "3.2.3", "prettier-plugin-packagejson": "2.4.5", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3", "typescript": "^5.1.6"}, "exports": {"./prototype": {"require": "./lowcode_lib/meta.js", "import": "./lowcode_es/meta.js"}, "./prototypeView": {"require": "./lowcode_lib/view.js", "import": "./lowcode_es/view.js"}, "./*": "./*", ".": {"import": "./es/index.js", "require": "./lib/index.js"}}, "componentConfig": {"materialSchema": "https://unpkg.com/leopard-web-qbi@0.1.0/build/lowcode/assets-prod.json"}, "lcMeta": {"type": "component"}}